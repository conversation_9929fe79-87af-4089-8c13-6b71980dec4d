<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * External API for tool_licensetrackeradmin plugin.
 *
 * @package    tool_licensetrackeradmin
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace tool_licensetrackeradmin\external;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->libdir . '/externallib.php');
require_once($CFG->dirroot . '/local/licensetracker/lib.php');

use external_api;
use external_function_parameters;
use external_value;
use external_single_structure;
use external_multiple_structure;

/**
 * External API class for license tracker.
 */
class licensetracker_api extends external_api {

    /**
     * Generate keys parameters.
     *
     * @return external_function_parameters
     */
    public static function generate_keys_parameters() {
        return new external_function_parameters(
            array(
                'numberofkeys' => new external_value(PARAM_INT, 'Number of keys to generate'),
                'partnerid' => new external_value(PARAM_INT, 'Partner ID'),
                'coursetypeid' => new external_value(PARAM_INT, 'Course type ID'),
                'validfrom' => new external_value(PARAM_INT, 'Valid from timestamp', VALUE_OPTIONAL),
                'expireson' => new external_value(PARAM_INT, 'Expires on timestamp', VALUE_OPTIONAL),
            )
        );
    }

    /**
     * Generate license keys.
     *
     * @param int $numberofkeys Number of keys to generate
     * @param int $partnerid Partner ID
     * @param int $coursetypeid Course type ID
     * @param int $validfrom Valid from timestamp
     * @param int $expireson Expires on timestamp
     * @return array
     */
    public static function generate_keys($numberofkeys, $partnerid, $coursetypeid, $validfrom = null, $expireson = null) {
        global $DB, $USER;

        // Validate parameters
        $params = self::validate_parameters(self::generate_keys_parameters(), array(
            'numberofkeys' => $numberofkeys,
            'partnerid' => $partnerid,
            'coursetypeid' => $coursetypeid,
            'validfrom' => $validfrom,
            'expireson' => $expireson
        ));

        // Check permissions
        $context = \context_system::instance();
        require_capability('local/licensetracker:manageallkeys', $context);

        // Validate inputs
        if ($params['numberofkeys'] < 1 || $params['numberofkeys'] > 10000) {
            throw new \invalid_parameter_exception('Number of keys must be between 1 and 10,000');
        }

        $partner = $DB->get_record('local_lt_partners', array('id' => $params['partnerid']));
        if (!$partner) {
            throw new \invalid_parameter_exception('Partner not found');
        }

        $coursetype = $DB->get_record('local_lt_coursetypes', array('id' => $params['coursetypeid']));
        if (!$coursetype) {
            throw new \invalid_parameter_exception('Course type not found');
        }

        $generated = array();
        $now = time();

        // Generate keys
        for ($i = 0; $i < $params['numberofkeys']; $i++) {
            try {
                $keystring = local_licensetracker_generate_unique_key();
                
                $keydata = new \stdClass();
                $keydata->keystring = $keystring;
                $keydata->coursetypeid = $params['coursetypeid'];
                $keydata->partnerid = $params['partnerid'];
                $keydata->status = 'available';
                $keydata->timecreated = $now;
                $keydata->timemodified = $now;
                
                if ($params['validfrom']) {
                    $keydata->validfrom = $params['validfrom'];
                }
                
                if ($params['expireson']) {
                    $keydata->expireson = $params['expireson'];
                }

                $keyid = $DB->insert_record('local_lt_keys', $keydata);
                $generated[] = array(
                    'id' => $keyid,
                    'keystring' => $keystring,
                    'status' => 'available'
                );

            } catch (\Exception $e) {
                // Log error but continue
                debugging('Error generating key: ' . $e->getMessage());
            }
        }

        return array(
            'success' => true,
            'generated' => count($generated),
            'keys' => $generated
        );
    }

    /**
     * Generate keys return values.
     *
     * @return external_single_structure
     */
    public static function generate_keys_returns() {
        return new external_single_structure(
            array(
                'success' => new external_value(PARAM_BOOL, 'Success status'),
                'generated' => new external_value(PARAM_INT, 'Number of keys generated'),
                'keys' => new external_multiple_structure(
                    new external_single_structure(
                        array(
                            'id' => new external_value(PARAM_INT, 'Key ID'),
                            'keystring' => new external_value(PARAM_TEXT, 'Key string'),
                            'status' => new external_value(PARAM_TEXT, 'Key status')
                        )
                    )
                )
            )
        );
    }

    /**
     * Check key status parameters.
     *
     * @return external_function_parameters
     */
    public static function check_key_status_parameters() {
        return new external_function_parameters(
            array(
                'keystring' => new external_value(PARAM_TEXT, 'License key string'),
            )
        );
    }

    /**
     * Check license key status.
     *
     * @param string $keystring License key string
     * @return array
     */
    public static function check_key_status($keystring) {
        global $DB;

        // Validate parameters
        $params = self::validate_parameters(self::check_key_status_parameters(), array(
            'keystring' => $keystring
        ));

        $key = $DB->get_record('local_lt_keys', array('keystring' => $params['keystring']));
        
        if (!$key) {
            return array(
                'found' => false,
                'status' => 'not_found',
                'valid' => false
            );
        }

        $valid = local_licensetracker_validate_key($params['keystring']);

        return array(
            'found' => true,
            'status' => $key->status,
            'valid' => (bool)$valid,
            'partnerid' => $key->partnerid,
            'coursetypeid' => $key->coursetypeid,
            'validfrom' => $key->validfrom,
            'expireson' => $key->expireson
        );
    }

    /**
     * Check key status return values.
     *
     * @return external_single_structure
     */
    public static function check_key_status_returns() {
        return new external_single_structure(
            array(
                'found' => new external_value(PARAM_BOOL, 'Whether key was found'),
                'status' => new external_value(PARAM_TEXT, 'Key status'),
                'valid' => new external_value(PARAM_BOOL, 'Whether key is valid for use'),
                'partnerid' => new external_value(PARAM_INT, 'Partner ID', VALUE_OPTIONAL),
                'coursetypeid' => new external_value(PARAM_INT, 'Course type ID', VALUE_OPTIONAL),
                'validfrom' => new external_value(PARAM_INT, 'Valid from timestamp', VALUE_OPTIONAL),
                'expireson' => new external_value(PARAM_INT, 'Expires on timestamp', VALUE_OPTIONAL)
            )
        );
    }

    /**
     * Use key parameters.
     *
     * @return external_function_parameters
     */
    public static function use_key_parameters() {
        return new external_function_parameters(
            array(
                'keystring' => new external_value(PARAM_TEXT, 'License key string'),
                'userid' => new external_value(PARAM_INT, 'User ID'),
                'ipaddress' => new external_value(PARAM_TEXT, 'IP address', VALUE_OPTIONAL),
                'useragent' => new external_value(PARAM_TEXT, 'User agent', VALUE_OPTIONAL),
            )
        );
    }

    /**
     * Use a license key.
     *
     * @param string $keystring License key string
     * @param int $userid User ID
     * @param string $ipaddress IP address
     * @param string $useragent User agent
     * @return array
     */
    public static function use_key($keystring, $userid, $ipaddress = '', $useragent = '') {
        global $DB;

        // Validate parameters
        $params = self::validate_parameters(self::use_key_parameters(), array(
            'keystring' => $keystring,
            'userid' => $userid,
            'ipaddress' => $ipaddress,
            'useragent' => $useragent
        ));

        // Validate key
        $key = local_licensetracker_validate_key($params['keystring']);
        if (!$key) {
            return array(
                'success' => false,
                'error' => 'Invalid or unavailable key'
            );
        }

        // Validate user
        $user = $DB->get_record('user', array('id' => $params['userid']));
        if (!$user) {
            return array(
                'success' => false,
                'error' => 'User not found'
            );
        }

        // Update key status
        $success = local_licensetracker_update_key_status(
            $key->id, 
            'used', 
            $params['userid'], 
            $params['ipaddress'], 
            $params['useragent']
        );

        if ($success) {
            return array(
                'success' => true,
                'keyid' => $key->id,
                'coursetypeid' => $key->coursetypeid,
                'partnerid' => $key->partnerid
            );
        } else {
            return array(
                'success' => false,
                'error' => 'Failed to update key status'
            );
        }
    }

    /**
     * Use key return values.
     *
     * @return external_single_structure
     */
    public static function use_key_returns() {
        return new external_single_structure(
            array(
                'success' => new external_value(PARAM_BOOL, 'Success status'),
                'error' => new external_value(PARAM_TEXT, 'Error message', VALUE_OPTIONAL),
                'keyid' => new external_value(PARAM_INT, 'Key ID', VALUE_OPTIONAL),
                'coursetypeid' => new external_value(PARAM_INT, 'Course type ID', VALUE_OPTIONAL),
                'partnerid' => new external_value(PARAM_INT, 'Partner ID', VALUE_OPTIONAL)
            )
        );
    }
}

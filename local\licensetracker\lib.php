<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Library functions for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

/**
 * Check if user is an administrator with license tracker permissions.
 *
 * @param int $userid User ID (optional, defaults to current user)
 * @return bool
 */
function local_licensetracker_is_admin($userid = null) {
    global $USER;
    
    if ($userid === null) {
        $userid = $USER->id;
    }
    
    $context = context_system::instance();
    return has_capability('local/licensetracker:manageallkeys', $context, $userid);
}

/**
 * Check if user is a partner.
 *
 * @param int $userid User ID (optional, defaults to current user)
 * @return bool
 */
function local_licensetracker_is_partner($userid = null) {
    global $USER;
    
    if ($userid === null) {
        $userid = $USER->id;
    }
    
    $context = context_system::instance();
    return has_capability('local/licensetracker:viewpartnerkeys', $context, $userid);
}

/**
 * Get partner information for a given Moodle user.
 *
 * @param int $userid User ID (optional, defaults to current user)
 * @return object|false Partner object or false if not found
 */
function local_licensetracker_get_partner_for_user($userid = null) {
    global $DB, $USER;
    
    if ($userid === null) {
        $userid = $USER->id;
    }
    
    return $DB->get_record('local_lt_partners', array('moodleuserid' => $userid));
}

/**
 * Generate a unique license key.
 *
 * @return string Unique license key
 */
function local_licensetracker_generate_unique_key() {
    global $DB;
    
    // Get key format from admin tool settings (default format if not set)
    $keyformat = get_config('tool_licensetrackeradmin', 'keyformat');
    if (empty($keyformat)) {
        $keyformat = json_encode([
            'segments' => 4,
            'segment_length' => 4,
            'separator' => '-',
            'charset' => 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
        ]);
    }
    
    $format = json_decode($keyformat, true);
    $charset = $format['charset'];
    $segments = $format['segments'];
    $segmentlength = $format['segment_length'];
    $separator = $format['separator'];
    
    $maxattempts = 100;
    $attempts = 0;
    
    do {
        $key = '';
        for ($i = 0; $i < $segments; $i++) {
            if ($i > 0) {
                $key .= $separator;
            }
            for ($j = 0; $j < $segmentlength; $j++) {
                $key .= $charset[random_int(0, strlen($charset) - 1)];
            }
        }
        
        $exists = $DB->record_exists('local_lt_keys', array('keystring' => $key));
        $attempts++;
        
    } while ($exists && $attempts < $maxattempts);
    
    if ($exists) {
        throw new moodle_exception('error:duplicatekey', 'local_licensetracker');
    }
    
    return $key;
}

/**
 * Get all license keys with optional filtering.
 *
 * @param array $filters Optional filters (partner, coursetype, status, search)
 * @param int $limitfrom Starting record
 * @param int $limitnum Number of records
 * @return array Array of key records
 */
function local_licensetracker_get_keys($filters = array(), $limitfrom = 0, $limitnum = 0) {
    global $DB;
    
    $sql = "SELECT k.*, p.partnername, ct.name as coursetypename, u.firstname, u.lastname
            FROM {local_lt_keys} k
            LEFT JOIN {local_lt_partners} p ON k.partnerid = p.id
            LEFT JOIN {local_lt_coursetypes} ct ON k.coursetypeid = ct.id
            LEFT JOIN {user} u ON k.usedbyuserid = u.id
            WHERE 1=1";
    
    $params = array();
    
    if (!empty($filters['partner'])) {
        $sql .= " AND k.partnerid = :partnerid";
        $params['partnerid'] = $filters['partner'];
    }
    
    if (!empty($filters['coursetype'])) {
        $sql .= " AND k.coursetypeid = :coursetypeid";
        $params['coursetypeid'] = $filters['coursetype'];
    }
    
    if (!empty($filters['status'])) {
        $sql .= " AND k.status = :status";
        $params['status'] = $filters['status'];
    }
    
    if (!empty($filters['search'])) {
        $sql .= " AND k.keystring LIKE :search";
        $params['search'] = '%' . $filters['search'] . '%';
    }
    
    $sql .= " ORDER BY k.timecreated DESC";
    
    return $DB->get_records_sql($sql, $params, $limitfrom, $limitnum);
}

/**
 * Update license key status.
 *
 * @param int $keyid Key ID
 * @param string $status New status
 * @param int $userid User ID who used the key (optional)
 * @param string $ipaddress IP address (optional)
 * @param string $useragent User agent (optional)
 * @return bool Success
 */
function local_licensetracker_update_key_status($keyid, $status, $userid = null, $ipaddress = null, $useragent = null) {
    global $DB;
    
    $updatedata = array(
        'id' => $keyid,
        'status' => $status,
        'timemodified' => time()
    );
    
    if ($status === 'used' && $userid) {
        $updatedata['usedbyuserid'] = $userid;
        $updatedata['dateused'] = time();
        $updatedata['ipaddressused'] = $ipaddress;
        $updatedata['useragent'] = $useragent;
    }
    
    return $DB->update_record('local_lt_keys', $updatedata);
}

/**
 * Get partner statistics.
 *
 * @param int $partnerid Partner ID (optional, for specific partner stats)
 * @return object Statistics object
 */
function local_licensetracker_get_partner_stats($partnerid = null) {
    global $DB;
    
    $sql = "SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'available' THEN 1 ELSE 0 END) as available,
                SUM(CASE WHEN status = 'used' THEN 1 ELSE 0 END) as used,
                SUM(CASE WHEN status = 'revoked' THEN 1 ELSE 0 END) as revoked
            FROM {local_lt_keys}";
    
    $params = array();
    
    if ($partnerid) {
        $sql .= " WHERE partnerid = :partnerid";
        $params['partnerid'] = $partnerid;
    }
    
    return $DB->get_record_sql($sql, $params);
}

/**
 * Validate a license key.
 *
 * @param string $keystring The license key string
 * @return object|false Key record if valid, false otherwise
 */
function local_licensetracker_validate_key($keystring) {
    global $DB;
    
    // Check if key exists and is available
    $key = $DB->get_record('local_lt_keys', array('keystring' => $keystring, 'status' => 'available'));
    
    if (!$key) {
        return false;
    }
    
    $now = time();
    
    // Check validfrom date
    if ($key->validfrom && $now < $key->validfrom) {
        return false;
    }
    
    // Check expireson date
    if ($key->expireson && $now > $key->expireson) {
        return false;
    }
    
    return $key;
}

/**
 * Get all partners.
 *
 * @return array Array of partner records
 */
function local_licensetracker_get_partners() {
    global $DB;
    
    $sql = "SELECT p.*, u.firstname, u.lastname, u.email as useremail
            FROM {local_lt_partners} p
            LEFT JOIN {user} u ON p.moodleuserid = u.id
            ORDER BY p.partnername";
    
    return $DB->get_records_sql($sql);
}

/**
 * Get all course types.
 *
 * @return array Array of course type records
 */
function local_licensetracker_get_coursetypes() {
    global $DB;
    
    $sql = "SELECT ct.*, c.fullname as coursename, r.shortname as rolename
            FROM {local_lt_coursetypes} ct
            LEFT JOIN {course} c ON ct.moodlecourseid = c.id
            LEFT JOIN {role} r ON ct.defaultroleid = r.id
            ORDER BY ct.name";
    
    return $DB->get_records_sql($sql);
}

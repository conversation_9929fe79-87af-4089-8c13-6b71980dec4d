<?xml version="1.0" encoding="UTF-8" ?>
<XMLDB PATH="local/licensetracker/db" VERSION="20250628" COMMENT="XMLDB file for Moodle local/licensetracker"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="../../../lib/xmldb/xmldb.xsd"
>
  <TABLES>
    <TABLE NAME="local_lt_coursetypes" COMMENT="Course type definitions">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="name" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="description" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="defaultprice" TYPE="number" LENGTH="10" NOTNULL="true" DEFAULT="0" DECIMALS="2" SEQUENCE="false"/>
        <FIELD NAME="moodlecourseid" TYPE="int" LENGTH="10" NOTNULL="true" DEFAULT="0" SEQUENCE="false"/>
        <FIELD NAME="enrolmethod" TYPE="char" LENGTH="50" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="defaultroleid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
        <KEY NAME="moodlecourseid" TYPE="foreign" FIELDS="moodlecourseid" REFTABLE="course" REFFIELDS="id"/>
        <KEY NAME="defaultroleid" TYPE="foreign" FIELDS="defaultroleid" REFTABLE="role" REFFIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="name" UNIQUE="true" FIELDS="name"/>
      </INDEXES>
    </TABLE>
    
    <TABLE NAME="local_lt_partners" COMMENT="Partner information">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="moodleuserid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="partnername" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="contactemail" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="country" TYPE="char" LENGTH="2" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="lang" TYPE="char" LENGTH="15" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
        <KEY NAME="moodleuserid" TYPE="foreign" FIELDS="moodleuserid" REFTABLE="user" REFFIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="partnername" UNIQUE="true" FIELDS="partnername"/>
        <INDEX NAME="contactemail" UNIQUE="false" FIELDS="contactemail"/>
      </INDEXES>
    </TABLE>
    
    <TABLE NAME="local_lt_partner_courses" COMMENT="Partner-specific course type prices">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="partnerid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="coursetypeid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="price" TYPE="number" LENGTH="10" NOTNULL="true" DEFAULT="0" DECIMALS="2" SEQUENCE="false"/>
        <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
        <KEY NAME="partnerid" TYPE="foreign" FIELDS="partnerid" REFTABLE="local_lt_partners" REFFIELDS="id" ONDELETE="cascade"/>
        <KEY NAME="coursetypeid" TYPE="foreign" FIELDS="coursetypeid" REFTABLE="local_lt_coursetypes" REFFIELDS="id" ONDELETE="cascade"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="partner_coursetype" UNIQUE="true" FIELDS="partnerid,coursetypeid"/>
      </INDEXES>
    </TABLE>

    <TABLE NAME="local_lt_keys" COMMENT="License key details">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="keystring" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="coursetypeid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="partnerid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="status" TYPE="char" LENGTH="20" NOTNULL="true" DEFAULT="available" SEQUENCE="false"/>
        <FIELD NAME="usedbyuserid" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="timecreated" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="timemodified" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="dateused" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="ipaddressused" TYPE="char" LENGTH="45" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="validfrom" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="expireson" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false"/>
        <FIELD NAME="useragent" TYPE="text" NOTNULL="false" SEQUENCE="false"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
        <KEY NAME="coursetypeid" TYPE="foreign" FIELDS="coursetypeid" REFTABLE="local_lt_coursetypes" REFFIELDS="id"/>
        <KEY NAME="partnerid" TYPE="foreign" FIELDS="partnerid" REFTABLE="local_lt_partners" REFFIELDS="id"/>
        <KEY NAME="usedbyuserid" TYPE="foreign" FIELDS="usedbyuserid" REFTABLE="user" REFFIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="keystring" UNIQUE="true" FIELDS="keystring"/>
        <INDEX NAME="status" UNIQUE="false" FIELDS="status"/>
        <INDEX NAME="validfrom" UNIQUE="false" FIELDS="validfrom"/>
        <INDEX NAME="expireson" UNIQUE="false" FIELDS="expireson"/>
      </INDEXES>
    </TABLE>
  </TABLES>
</XMLDB>

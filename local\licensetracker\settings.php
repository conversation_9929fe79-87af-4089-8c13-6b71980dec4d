<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Settings for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

if ($hassiteconfig) {
    // Main license tracker page
    $ADMIN->add('localplugins', new admin_externalpage(
        'local_licensetracker',
        get_string('licensetracker', 'local_licensetracker'),
        new moodle_url('/local/licensetracker/index.php'),
        'local/licensetracker:manageallkeys'
    ));

    // License tracker settings
    $settings = new admin_settingpage('local_licensetracker_settings',
                                     get_string('settings', 'local_licensetracker'));

    if ($ADMIN->fulltree) {
        // Enable/disable license tracking
        $settings->add(new admin_setting_configcheckbox(
            'local_licensetracker/enabled',
            get_string('enabled', 'local_licensetracker'),
            get_string('enabled_desc', 'local_licensetracker'),
            1
        ));

        // Enforce license for all students
        $settings->add(new admin_setting_configcheckbox(
            'local_licensetracker/enforce_all',
            get_string('enforce_all', 'local_licensetracker'),
            get_string('enforce_all_desc', 'local_licensetracker'),
            1
        ));

        // Local countries (students who don't need licenses)
        $settings->add(new admin_setting_configtextarea(
            'local_licensetracker/local_countries',
            get_string('local_countries', 'local_licensetracker'),
            get_string('local_countries_desc', 'local_licensetracker'),
            '',
            PARAM_TEXT
        ));

        // Default key format
        $settings->add(new admin_setting_configtextarea(
            'local_licensetracker/key_format',
            get_string('key_format', 'local_licensetracker'),
            get_string('key_format_desc', 'local_licensetracker'),
            '{"segments": 4, "segment_length": 4, "separator": "-", "charset": "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"}',
            PARAM_TEXT
        ));

        // Email notifications
        $settings->add(new admin_setting_configcheckbox(
            'local_licensetracker/email_notifications',
            get_string('email_notifications', 'local_licensetracker'),
            get_string('email_notifications_desc', 'local_licensetracker'),
            1
        ));

        // Auto-cleanup expired keys
        $settings->add(new admin_setting_configcheckbox(
            'local_licensetracker/auto_cleanup',
            get_string('auto_cleanup', 'local_licensetracker'),
            get_string('auto_cleanup_desc', 'local_licensetracker'),
            0
        ));
    }

    $ADMIN->add('localplugins', $settings);
}

<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Settings for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

if ($hassiteconfig) {
    $settings = new admin_settingpage('local_licensetracker', get_string('pluginname', 'local_licensetracker'));

    $settings->add(new admin_setting_configcheckbox(
        'local_licensetracker/enabled',
        get_string('enabled', 'core'),
        get_string('enabled_desc', 'local_licensetracker'),
        1
    ));

    $ADMIN->add('localplugins', $settings);
}

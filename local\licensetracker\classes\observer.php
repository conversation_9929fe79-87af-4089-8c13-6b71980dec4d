<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mood<PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Event observer for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace local_licensetracker;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->dirroot . '/local/licensetracker/lib.php');

/**
 * Event observer class for license tracker.
 */
class observer {

    /**
     * Observe user creation events to enforce license validation.
     *
     * @param \core\event\user_created $event
     */
    public static function user_created(\core\event\user_created $event) {
        global $DB, $SESSION;

        $userid = $event->objectid;
        $user = $DB->get_record('user', array('id' => $userid));

        if (!$user) {
            return;
        }

        // Check if this is a self-registration (not admin created)
        if ($user->auth !== 'licensetracker' && $event->userid != $userid) {
            // This is admin-created user, allow it
            return;
        }

        // Check if license key validation is enabled
        $config = get_config('auth_licensetracker');
        if (empty($config->enabled)) {
            return;
        }

        // Check if this user needs a license key
        if (!self::user_requires_license($user)) {
            return;
        }

        // Check if license key was provided and validated during registration
        if (empty($SESSION->licensetracker_validated_key)) {
            // No valid license key - delete the user and throw exception
            $DB->delete_records('user', array('id' => $userid));
            throw new \moodle_exception('registrationrequireskey', 'auth_licensetracker');
        }

        // License key was validated - process enrollment
        $keyid = $SESSION->licensetracker_validated_key;
        $key = $DB->get_record('local_lt_keys', array('id' => $keyid));
        
        if ($key) {
            // Update key status
            local_licensetracker_update_key_status($key->id, 'used', $userid, 
                                                  getremoteaddr(), $_SERVER['HTTP_USER_AGENT'] ?? '');
            
            // Enroll user in course
            self::enroll_user_in_course($userid, $key->coursetypeid);
            
            // Set partner language preference
            self::set_partner_language_preference($userid, $key->partnerid);
        }

        // Clear session variable
        unset($SESSION->licensetracker_validated_key);
    }

    /**
     * Check if user requires a license key.
     *
     * @param object $user User object
     * @return bool
     */
    private static function user_requires_license($user) {
        $config = get_config('auth_licensetracker');
        
        // If require key for all is enabled, always require key
        if (!empty($config->requirekeyforall)) {
            return true;
        }

        // Check if user's country requires key (local vs global logic)
        if (!empty($config->localcountries)) {
            $localcountries = array_map('trim', explode(',', $config->localcountries));
            if (in_array($user->country, $localcountries)) {
                return false; // Local student, no key required
            }
        }

        return true; // Global student, key required
    }

    /**
     * Enroll user in course based on course type settings.
     *
     * @param int $userid
     * @param int $coursetypeid
     */
    private static function enroll_user_in_course($userid, $coursetypeid) {
        global $DB;

        $coursetype = $DB->get_record('local_lt_coursetypes', array('id' => $coursetypeid));
        if (!$coursetype) {
            return;
        }

        $course = $DB->get_record('course', array('id' => $coursetype->moodlecourseid));
        if (!$course) {
            return;
        }

        $enrolmethod = $coursetype->enrolmethod ?: 'manual';
        $roleid = $coursetype->defaultroleid ?: 5; // Default to student role

        // Get enrollment plugin
        $enrol = enrol_get_plugin($enrolmethod);
        if (!$enrol) {
            $enrol = enrol_get_plugin('manual'); // Fallback to manual
            $enrolmethod = 'manual';
        }

        // Get or create enrollment instance
        $instance = $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => $enrolmethod));
        if (!$instance) {
            // Create new instance if it doesn't exist
            $instanceid = $enrol->add_instance($course);
            $instance = $DB->get_record('enrol', array('id' => $instanceid));
        }

        // Enroll the user
        $enrol->enrol_user($instance, $userid, $roleid, time(), 0, ENROL_USER_ACTIVE);
    }

    /**
     * Set partner language preference for new user.
     *
     * @param int $userid
     * @param int $partnerid
     */
    private static function set_partner_language_preference($userid, $partnerid) {
        global $DB;

        try {
            $partner = $DB->get_record('local_lt_partners', array('id' => $partnerid));
            if ($partner && !empty($partner->lang)) {
                $user = new \stdClass();
                $user->id = $userid;
                $user->lang = $partner->lang;
                $DB->update_record('user', $user);
            }
        } catch (\Exception $e) {
            // Log error but don't fail registration
            debugging('Failed to set partner language: ' . $e->getMessage());
        }
    }
}

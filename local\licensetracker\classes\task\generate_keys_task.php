<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Adhoc task for generating license keys in bulk.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

namespace local_licensetracker\task;

defined('MOODLE_INTERNAL') || die();

require_once($CFG->dirroot . '/local/licensetracker/lib.php');

/**
 * Adhoc task for generating license keys in bulk.
 */
class generate_keys_task extends \core\task\adhoc_task {

    /**
     * Get a descriptive name for this task.
     *
     * @return string
     */
    public function get_name() {
        return get_string('generatekeys', 'local_licensetracker');
    }

    /**
     * Execute the task.
     */
    public function execute() {
        global $DB;

        $data = $this->get_custom_data();
        
        if (!isset($data->numberofkeys) || !isset($data->partnerid) || !isset($data->coursetypeid)) {
            mtrace('Invalid task data provided');
            return;
        }

        $numberofkeys = $data->numberofkeys;
        $partnerid = $data->partnerid;
        $coursetypeid = $data->coursetypeid;
        $validfrom = isset($data->validfrom) ? $data->validfrom : null;
        $expireson = isset($data->expireson) ? $data->expireson : null;
        $userid = isset($data->userid) ? $data->userid : null;

        mtrace("Starting generation of {$numberofkeys} keys for partner {$partnerid}, course type {$coursetypeid}");

        $generated = 0;
        $errors = 0;
        $now = time();

        for ($i = 0; $i < $numberofkeys; $i++) {
            try {
                $keystring = local_licensetracker_generate_unique_key();
                
                $keydata = new \stdClass();
                $keydata->keystring = $keystring;
                $keydata->coursetypeid = $coursetypeid;
                $keydata->partnerid = $partnerid;
                $keydata->status = 'available';
                $keydata->timecreated = $now;
                $keydata->timemodified = $now;
                
                if ($validfrom) {
                    $keydata->validfrom = $validfrom;
                }
                
                if ($expireson) {
                    $keydata->expireson = $expireson;
                }

                $DB->insert_record('local_lt_keys', $keydata);
                $generated++;

                // Progress update every 100 keys
                if ($generated % 100 == 0) {
                    mtrace("Generated {$generated} of {$numberofkeys} keys");
                }

            } catch (\Exception $e) {
                mtrace("Error generating key: " . $e->getMessage());
                $errors++;
                
                // Stop if too many errors
                if ($errors > 10) {
                    mtrace("Too many errors, stopping task");
                    break;
                }
            }
        }

        mtrace("Task completed. Generated: {$generated}, Errors: {$errors}");

        // Send notification to user if userid provided
        if ($userid && $generated > 0) {
            $this->send_completion_notification($userid, $generated, $errors);
        }
    }

    /**
     * Send completion notification to user.
     *
     * @param int $userid User ID
     * @param int $generated Number of keys generated
     * @param int $errors Number of errors
     */
    private function send_completion_notification($userid, $generated, $errors) {
        global $DB;

        try {
            $user = $DB->get_record('user', array('id' => $userid));
            if (!$user) {
                return;
            }

            $subject = get_string('keygenerated', 'local_licensetracker');
            $message = "Key generation task completed.\n\n";
            $message .= "Keys generated: {$generated}\n";
            if ($errors > 0) {
                $message .= "Errors encountered: {$errors}\n";
            }

            $messagedata = new \core\message\message();
            $messagedata->component = 'local_licensetracker';
            $messagedata->name = 'keygeneration';
            $messagedata->userfrom = \core_user::get_noreply_user();
            $messagedata->userto = $user;
            $messagedata->subject = $subject;
            $messagedata->fullmessage = $message;
            $messagedata->fullmessageformat = FORMAT_PLAIN;
            $messagedata->fullmessagehtml = nl2br($message);
            $messagedata->smallmessage = $subject;

            message_send($messagedata);

        } catch (\Exception $e) {
            mtrace("Error sending notification: " . $e->getMessage());
        }
    }
}

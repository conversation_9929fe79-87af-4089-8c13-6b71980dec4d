<?php
// This file is part of Moodle - http://moodle.org/
//
// Mood<PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Admin generate keys tab content for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$form = new \local_licensetracker\form\key_generation_form();

if ($form->is_cancelled()) {
    redirect(new moodle_url('/local/licensetracker/index.php', array('tab' => 'generatekeys')));
} else if ($data = $form->get_data()) {
    
    $numberofkeys = $data->numberofkeys;
    $partnerid = $data->partnerid;
    $coursetypeid = $data->coursetypeid;
    $validfrom = !empty($data->validfrom) ? $data->validfrom : null;
    $expireson = !empty($data->expireson) ? $data->expireson : null;
    
    if ($numberofkeys > 100) {
        // Queue as async task for large batches
        $taskdata = new stdClass();
        $taskdata->numberofkeys = $numberofkeys;
        $taskdata->partnerid = $partnerid;
        $taskdata->coursetypeid = $coursetypeid;
        $taskdata->validfrom = $validfrom;
        $taskdata->expireson = $expireson;
        $taskdata->userid = $USER->id;
        
        $task = new \local_licensetracker\task\generate_keys_task();
        $task->set_custom_data($taskdata);
        \core\task\manager::queue_adhoc_task($task);
        
        redirect($PAGE->url, get_string('taskqueued', 'local_licensetracker'), null, \core\output\notification::NOTIFY_INFO);
        
    } else {
        // Generate keys immediately for small batches
        $generated = 0;
        $now = time();
        
        for ($i = 0; $i < $numberofkeys; $i++) {
            try {
                $keystring = local_licensetracker_generate_unique_key();
                
                $keydata = new stdClass();
                $keydata->keystring = $keystring;
                $keydata->coursetypeid = $coursetypeid;
                $keydata->partnerid = $partnerid;
                $keydata->status = 'available';
                $keydata->timecreated = $now;
                $keydata->timemodified = $now;
                
                if ($validfrom) {
                    $keydata->validfrom = $validfrom;
                }
                
                if ($expireson) {
                    $keydata->expireson = $expireson;
                }
                
                $DB->insert_record('local_lt_keys', $keydata);
                $generated++;
                
            } catch (Exception $e) {
                // Log error but continue
                debugging('Error generating key: ' . $e->getMessage());
            }
        }
        
        if ($generated == 1) {
            $message = get_string('keygenerated', 'local_licensetracker');
        } else {
            $message = get_string('keysgenerated', 'local_licensetracker', $generated);
        }
        
        redirect($PAGE->url, $message, null, \core\output\notification::NOTIFY_SUCCESS);
    }
}

$output = '';
$output .= '<div class="card">';
$output .= '<div class="card-header">';
$output .= '<h3>' . get_string('generatekeys', 'local_licensetracker') . '</h3>';
$output .= '</div>';
$output .= '<div class="card-body">';
$output .= $form->render();
$output .= '</div>';
$output .= '</div>';

return $output;

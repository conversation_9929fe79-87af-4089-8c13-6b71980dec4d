<!-- This file is part of Moodle - http://moodle.org/

     Mo<PERSON>le is free software: you can redistribute it and/or modify
     it under the terms of the GNU General Public License as published by
     the Free Software Foundation, either version 3 of the License, or
     (at your option) any later version.

     Moodle is distributed in the hope that it will be useful,
     but WITHOUT ANY WARRANTY; without even the implied warranty of
     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
     GNU General Public License for more details.

     You should have received a copy of the GNU General Public License
     along with Moodle.  If not, see <http://www.gnu.org/licenses/>. -->

<!-- Configuration form for auth_licensetracker plugin -->

<table cellspacing="0" cellpadding="5" border="0">
    <tr valign="top">
        <td align="right">
            <label for="enabled"><?php print_string('enabled', 'auth_licensetracker') ?>:</label>
        </td>
        <td>
            <input type="checkbox" name="enabled" id="enabled" value="1" <?php echo !empty($config->enabled) ? 'checked="checked"' : '' ?> />
            <br />
            <small><?php print_string('enabled_desc', 'auth_licensetracker') ?></small>
        </td>
    </tr>
    
    <tr valign="top">
        <td align="right">
            <label for="requirekeyforall"><?php print_string('requirekeyforall', 'auth_licensetracker') ?>:</label>
        </td>
        <td>
            <input type="checkbox" name="requirekeyforall" id="requirekeyforall" value="1" <?php echo !empty($config->requirekeyforall) ? 'checked="checked"' : '' ?> />
            <br />
            <small><?php print_string('requirekeyforall_desc', 'auth_licensetracker') ?></small>
        </td>
    </tr>
    
    <tr valign="top">
        <td align="right">
            <label for="localcountries"><?php print_string('localcountries', 'auth_licensetracker') ?>:</label>
        </td>
        <td>
            <textarea name="localcountries" id="localcountries" rows="3" cols="50"><?php echo isset($config->localcountries) ? s($config->localcountries) : '' ?></textarea>
            <br />
            <small><?php print_string('localcountries_desc', 'auth_licensetracker') ?></small>
        </td>
    </tr>
</table>

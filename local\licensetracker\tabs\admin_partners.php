<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Admin partners tab content for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$action = optional_param('action', '', PARAM_ALPHA);
$partnerid = optional_param('partnerid', 0, PARAM_INT);

// Handle actions
if ($action && confirm_sesskey()) {
    switch ($action) {
        case 'delete':
            if ($partnerid) {
                // Check if partner has keys
                $keycount = $DB->count_records('local_lt_keys', array('partnerid' => $partnerid));
                if ($keycount > 0) {
                    redirect($PAGE->url, get_string('keysinuse', 'local_licensetracker'), 
                            null, \core\output\notification::NOTIFY_ERROR);
                } else {
                    $DB->delete_records('local_lt_partner_courses', array('partnerid' => $partnerid));
                    $DB->delete_records('local_lt_partners', array('id' => $partnerid));
                    redirect($PAGE->url, get_string('partnerdeleted', 'local_licensetracker'), 
                            null, \core\output\notification::NOTIFY_SUCCESS);
                }
            }
            break;
    }
}

// Handle form submission for add/edit
if ($partnerid) {
    $partner = $DB->get_record('local_lt_partners', array('id' => $partnerid));
    $form = new \local_licensetracker\form\partner_form(null, null, 'post', '', null, true, $partner);
} else {
    $form = new \local_licensetracker\form\partner_form();
}

if ($form->is_cancelled()) {
    redirect(new moodle_url('/local/licensetracker/index.php', array('tab' => 'partners')));
} else if ($data = $form->get_data()) {
    
    $partnerdata = new stdClass();
    $partnerdata->partnername = $data->partnername;
    $partnerdata->contactemail = $data->contactemail;
    $partnerdata->country = $data->country;
    $partnerdata->lang = $data->lang;
    $partnerdata->moodleuserid = $data->moodleuserid;
    $partnerdata->timemodified = time();
    
    if (!empty($data->id)) {
        // Update existing partner
        $partnerdata->id = $data->id;
        $DB->update_record('local_lt_partners', $partnerdata);
        $message = get_string('partnerupdated', 'local_licensetracker');
    } else {
        // Create new partner
        $partnerdata->timecreated = time();
        $DB->insert_record('local_lt_partners', $partnerdata);
        $message = get_string('partneradded', 'local_licensetracker');
    }
    
    redirect(new moodle_url('/local/licensetracker/index.php', array('tab' => 'partners')), 
            $message, null, \core\output\notification::NOTIFY_SUCCESS);
}

$output = '';

// Add/Edit form
if ($action === 'add' || $action === 'edit') {
    $output .= '<div class="card mb-4">';
    $output .= '<div class="card-header">';
    $output .= '<h4>' . ($action === 'edit' ? get_string('editpartner', 'local_licensetracker') : get_string('addpartner', 'local_licensetracker')) . '</h4>';
    $output .= '</div>';
    $output .= '<div class="card-body">';
    $output .= $form->render();
    $output .= '</div>';
    $output .= '</div>';
} else {
    // Add button
    $output .= '<div class="mb-3">';
    $output .= '<a href="' . $CFG->wwwroot . '/local/licensetracker/index.php?tab=partners&action=add" class="btn btn-primary">';
    $output .= get_string('addpartner', 'local_licensetracker') . '</a>';
    $output .= '</div>';
}

// Partners table
$partners = local_licensetracker_get_partners();

$output .= '<div class="card">';
$output .= '<div class="card-header">';
$output .= '<h4>' . get_string('partners', 'local_licensetracker') . '</h4>';
$output .= '</div>';
$output .= '<div class="card-body">';

if (!empty($partners)) {
    $output .= '<div class="table-responsive">';
    $output .= '<table class="table table-striped table-hover">';
    $output .= '<thead class="thead-dark">';
    $output .= '<tr>';
    $output .= '<th>' . get_string('partnername', 'local_licensetracker') . '</th>';
    $output .= '<th>' . get_string('contactemail', 'local_licensetracker') . '</th>';
    $output .= '<th>' . get_string('country', 'local_licensetracker') . '</th>';
    $output .= '<th>' . get_string('language', 'local_licensetracker') . '</th>';
    $output .= '<th>' . get_string('moodleuser', 'local_licensetracker') . '</th>';
    $output .= '<th>' . get_string('actions', 'local_licensetracker') . '</th>';
    $output .= '</tr>';
    $output .= '</thead>';
    $output .= '<tbody>';
    
    foreach ($partners as $partner) {
        $output .= '<tr>';
        $output .= '<td>' . $partner->partnername . '</td>';
        $output .= '<td>' . $partner->contactemail . '</td>';
        $output .= '<td>' . ($partner->country ? get_string($partner->country, 'countries') : '') . '</td>';
        $output .= '<td>' . ($partner->lang ? get_string($partner->lang, 'langconfig') : '') . '</td>';
        $output .= '<td>' . ($partner->firstname ? $partner->firstname . ' ' . $partner->lastname : '') . '</td>';
        $output .= '<td>';
        $output .= '<div class="btn-group btn-group-sm">';
        $output .= '<a href="' . $CFG->wwwroot . '/local/licensetracker/index.php?tab=partners&action=edit&partnerid=' . $partner->id . '" class="btn btn-primary btn-sm">' . get_string('edit', 'local_licensetracker') . '</a>';
        $output .= '<a href="' . $CFG->wwwroot . '/local/licensetracker/index.php?tab=partners&action=delete&partnerid=' . $partner->id . '&sesskey=' . sesskey() . '" class="btn btn-danger btn-sm" onclick="return confirm(\'' . get_string('confirmdelete', 'core') . '\')">' . get_string('delete', 'local_licensetracker') . '</a>';
        $output .= '</div>';
        $output .= '</td>';
        $output .= '</tr>';
    }
    
    $output .= '</tbody>';
    $output .= '</table>';
    $output .= '</div>';
} else {
    $output .= '<div class="alert alert-info">';
    $output .= get_string('nopartnersavailable', 'local_licensetracker');
    $output .= '</div>';
}

$output .= '</div>';
$output .= '</div>';

return $output;

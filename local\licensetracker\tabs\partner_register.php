<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Partner student registration tab content for local_licensetracker plugin.
 *
 * @package    local_licensetracker
 * @copyright  2025 License Tracker
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$form = new \local_licensetracker\form\student_registration_form();

if ($form->is_cancelled()) {
    redirect(new moodle_url('/local/licensetracker/index.php', array('tab' => 'register')));
} else if ($data = $form->get_data()) {
    
    // Validate license key again
    $key = local_licensetracker_validate_key($data->licensekey);
    if (!$key || $key->partnerid != $partner->id) {
        redirect($PAGE->url, get_string('error:invalidkey', 'local_licensetracker'), 
                null, \core\output\notification::NOTIFY_ERROR);
    }
    
    // Get course type information
    $coursetype = $DB->get_record('local_lt_coursetypes', array('id' => $key->coursetypeid));
    if (!$coursetype) {
        redirect($PAGE->url, get_string('error:coursetypenotfound', 'local_licensetracker'), 
                null, \core\output\notification::NOTIFY_ERROR);
    }
    
    try {
        // Create user account
        $newuser = new stdClass();
        $newuser->username = $data->username;
        $newuser->firstname = $data->firstname;
        $newuser->lastname = $data->lastname;
        $newuser->email = $data->email;
        $newuser->password = hash_internal_user_password($data->password);
        $newuser->city = $data->city;
        $newuser->country = $data->country;
        $newuser->confirmed = 1;
        $newuser->auth = 'manual';
        $newuser->mnethostid = $CFG->mnet_localhost_id;
        $newuser->timecreated = time();
        $newuser->timemodified = time();
        
        // Set language from partner if available
        if (!empty($partner->lang)) {
            $newuser->lang = $partner->lang;
        }
        
        $userid = $DB->insert_record('user', $newuser);
        
        if (!$userid) {
            throw new Exception('Failed to create user account');
        }
        
        // Update license key status
        $ipaddress = getremoteaddr();
        $useragent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        local_licensetracker_update_key_status($key->id, 'used', $userid, $ipaddress, $useragent);
        
        // Enroll user in course
        $course = $DB->get_record('course', array('id' => $coursetype->moodlecourseid));
        if ($course) {
            $enrolmethod = $coursetype->enrolmethod ?: 'manual';
            $roleid = $coursetype->defaultroleid ?: 5; // Default to student role
            
            // Get enrollment plugin
            $enrol = enrol_get_plugin($enrolmethod);
            if (!$enrol) {
                $enrol = enrol_get_plugin('manual'); // Fallback to manual
                $enrolmethod = 'manual';
            }
            
            // Get or create enrollment instance
            $instance = $DB->get_record('enrol', array('courseid' => $course->id, 'enrol' => $enrolmethod));
            if (!$instance) {
                // Create new instance if it doesn't exist
                $instanceid = $enrol->add_instance($course);
                $instance = $DB->get_record('enrol', array('id' => $instanceid));
            }
            
            // Enroll the user
            $enrol->enrol_user($instance, $userid, $roleid, time(), 0, ENROL_USER_ACTIVE);
        }
        
        $message = get_string('studentregistered', 'local_licensetracker');
        if ($course) {
            $message .= ' ' . get_string('enrollmentsuccess', 'auth_licensetracker', format_string($course->fullname));
        }
        
        redirect($PAGE->url, $message, null, \core\output\notification::NOTIFY_SUCCESS);
        
    } catch (Exception $e) {
        redirect($PAGE->url, 'Registration failed: ' . $e->getMessage(), 
                null, \core\output\notification::NOTIFY_ERROR);
    }
}

$output = '';
$output .= '<div class="card">';
$output .= '<div class="card-header">';
$output .= '<h3>' . get_string('registernewstudent', 'local_licensetracker') . '</h3>';
$output .= '</div>';
$output .= '<div class="card-body">';

// Check if partner has available keys
$availablekeys = $DB->count_records('local_lt_keys',
                                   array('partnerid' => $partner->id, 'status' => 'available'));

if ($availablekeys == 0) {
    $output .= '<div class="alert alert-warning">';
    $output .= get_string('noavailablekeys', 'local_licensetracker');
    $output .= '</div>';
} else {
    $output .= '<div class="alert alert-info">';
    $output .= 'You have ' . $availablekeys . ' available license keys for student registration.';
    $output .= '</div>';
    
    $output .= $form->render();
}

$output .= '</div>';
$output .= '</div>';

return $output;
